<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="color-scheme" content="dark" />
    <title>任务报告：“星光守护者”计划</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&family=Orbitron:wght@400;600;800&display=swap" rel="stylesheet" />
    <link rel="stylesheet" href="./styles.css" />
  </head>
  <body>
    <canvas id="starfield" aria-hidden="true"></canvas>
    <div id="nebula-overlay" aria-hidden="true"></div>

    <header id="top-bar">
      <div class="brand">任务报告：“星光守护者”计划</div>
      <nav class="pager">
        <button id="prevBtn" class="nav-btn" aria-label="上一页">⟵ 上一页</button>
        <div id="pageIndicator" class="indicator">1 / 9</div>
        <button id="nextBtn" class="nav-btn" aria-label="下一页">下一页 ⟶</button>
      </nav>
    </header>

    <main id="pages" role="main" aria-live="polite">
      <!-- 第1页：封面 -->
      <section class="page" id="page1" data-title="封面">
        <div class="content center cover">
          <h1 class="title">任务报告：“星光守护者”计划</h1>
          <h2 class="subtitle">福建大学生安全知识竞赛 - 航行复盘</h2>
          <div class="scene">
            <div class="planet">
              <div class="atmosphere"></div>
              <div class="fujian-outline" title="福建地图轮廓"></div>
            </div>
            <div class="ship-warp" aria-label="守护者号"></div>
            <div class="trajectory"></div>
          </div>
        </div>
      </section>

      <!-- 第2页：舰桥船员 -->
      <section class="page" id="page2" data-title="舰桥船员">
        <div class="content">
          <h2 class="section-title">“守护者号”核心船员</h2>
          <div class="crew-grid">
            <div class="crew-card" data-name="赵明" data-role="舰长（主持）">
              <div class="holo"></div>
              <div class="card-header">赵明</div>
              <div class="card-role">舰长（主持）</div>
              <div class="card-desc">赵明：舰长（主持）。全程把控节奏与决策。</div>
            </div>
            <div class="crew-card" data-name="马佳平" data-role="大副（后台导演）">
              <div class="holo"></div>
              <div class="card-header">马佳平</div>
              <div class="card-role">大副（后台导演）</div>
              <div class="card-desc">马佳平：大副（后台导演）。调度全场资源与流程。</div>
            </div>
            <div class="crew-card" data-name="蔡庆强" data-role="总工程师（技术）">
              <div class="holo"></div>
              <div class="card-header">蔡庆强</div>
              <div class="card-role">总工程师（技术）</div>
              <div class="card-desc">蔡庆强：总工程师（技术）。系统稳定与方案落地。</div>
            </div>
            <div class="crew-card" data-name="陈柄宏" data-role="领航员（VJ）">
              <div class="holo"></div>
              <div class="card-header">陈柄宏</div>
              <div class="card-role">领航员（VJ）</div>
              <div class="card-desc">陈柄宏：领航员（VJ）。视觉节奏与现场氛围。</div>
            </div>
            <div class="crew-card" data-name="梁金隆" data-role="通讯官（DJ）">
              <div class="holo"></div>
              <div class="card-header">梁金隆</div>
              <div class="card-role">通讯官（DJ）</div>
              <div class="card-desc">梁金隆：通讯官（DJ）。能量场与信息节拍。</div>
            </div>
            <div class="crew-card" data-name="盛捷" data-role="科学官（计分）">
              <div class="holo"></div>
              <div class="card-header">盛捷</div>
              <div class="card-role">科学官（计分）</div>
              <div class="card-desc">盛捷：科学官（计分）。分数准确与公平公正。</div>
            </div>
          </div>
          <p class="hint">提示：将鼠标滑过任意卡片，查看角色描述原文</p>
        </div>
      </section>

      <!-- 第3页：航行日志 -->
      <section class="page" id="page3" data-title="航行日志">
        <div class="content">
          <h2 class="section-title">航行日志：闪亮的星尘记忆</h2>
          <div class="starmap">
            <button class="cluster" data-key="c1" aria-label="星团1"></button>
            <button class="cluster" data-key="c2" aria-label="星团2"></button>
            <button class="cluster" data-key="c3" aria-label="星团3"></button>
            <div class="cluster-glow"></div>
          </div>
          <div class="log-panel" aria-live="polite">
            <div class="log-content">点击任一星团以展开记忆</div>
          </div>
        </div>
      </section>

      <!-- 第4页：穿越小行星带 -->
      <section class="page" id="page4" data-title="穿越小行星带">
        <div class="content two-col">
          <h2 class="section-title">警报！穿越小行星带！</h2>
          <div class="col left">
            <button class="meteor" data-problem="score">计分表记录难题</button>
            <button class="meteor" data-problem="music">寻找合适的氛围音乐</button>
            <button class="meteor" data-problem="timer">计时器接口接触不良</button>
          </div>
          <div class="col right">
            <div class="evasion-area">
              <div class="ship-evade" aria-label="飞船"></div>
              <div class="asteroid-field"></div>
            </div>
            <div class="solution" aria-live="polite">点击左侧陨石查看应对方案与规避动画</div>
          </div>
        </div>
      </section>

      <!-- 第5页：来自过去的通讯 -->
      <section class="page" id="page5" data-title="来自过去的通讯">
        <div class="content center">
          <h2 class="section-title">捕获来自过去的通讯</h2>
          <div class="holo-projector">
            <div class="emitter"></div>
            <div class="holo-messages">
              <div class="holo-line" data-delay="0">“不要紧张，这只是一次很小的比赛。”</div>
              <div class="holo-line" data-delay="1">“做好份内事，学会拒绝。”</div>
              <div class="holo-line" data-delay="2">“别给自己太大压力，人生有很多容错率。”</div>
            </div>
          </div>
        </div>
      </section>

      <!-- 第6页：“啊哈！”时刻 -->
      <section class="page" id="page6" data-title="啊哈！时刻">
        <div class="content">
          <h2 class="section-title">啊哈！发现“效率”星云！</h2>
          <div class="radar-wrap">
            <div class="radar">
              <div class="sweep"></div>
              <div class="blip"></div>
            </div>
            <button class="nebula-target" aria-label="效率星云">效率星云</button>
          </div>
          <div class="discovery-card" aria-live="polite">
            <div class="card-title">工作方法的新发现</div>
            <div class="card-body">不必一味追求自动化和最新技术栈，在开发仅一人的情况下，优先满足核心需求才是王道。告别完美主义！</div>
          </div>
        </div>
      </section>

      <!-- 第7页：星光勋章授予仪式（铺垫） -->
      <section class="page" id="page7" data-title="颁奖铺垫">
        <div class="content">
          <h2 class="section-title">全舰广播：致敬我们的伙伴！</h2>
          <div class="bridge-screen">
            <div class="ticker">
              <div class="tick">匿名赞美：为CTO送上小红花</div>
              <div class="tick">匿名赞美：彩排时向云一直告诉我DJ的一些技巧</div>
              <div class="tick">匿名赞美：为大梁送上小红花</div>
              <div class="tick">匿名赞美：工程团队今晚表现稳如老狗</div>
              <div class="tick">匿名赞美：舞台前后配合默契，冲！</div>
            </div>
          </div>
        </div>
      </section>

      <!-- 第8页：星光勋章授予仪式（核心动画） -->
      <section class="page" id="page8" data-title="星光勋章">
        <div class="content">
          <h2 class="section-title">本航程的“星光勋章”获得者是……</h2>
          <div class="name-wall">
            <span class="name">赵明</span>
            <span class="name">马佳平</span>
            <span class="name">蔡庆强</span>
            <span class="name">陈柄宏</span>
            <span class="name">梁金隆</span>
            <span class="name">盛捷</span>
            <span class="name winner" id="winnerName">向云</span>
          </div>
          <div class="beams">
            <div class="beam beam-a"></div>
            <div class="beam beam-b"></div>
            <div class="beam beam-c"></div>
          </div>
          <div class="medal-wrap">
            <div class="medal"></div>
            <div class="medal-caption">
              <div class="medal-title">向云 - 团队最闪亮的星！</div>
              <div class="medal-desc">感谢你，在关键时刻给予的专业指导与支持！</div>
            </div>
          </div>
        </div>
      </section>

      <!-- 第9页：任务完成（总结） -->
      <section class="page" id="page9" data-title="任务完成">
        <div class="content center">
          <h2 class="section-title">任务完成，成功返航！</h2>
          <div class="dashboard">
            <div class="gauge" data-value="0.9">
              <div class="gauge-inner">
                <div class="gauge-needle"></div>
                <div class="gauge-label">任务完成度：<strong>4.5 / 5</strong></div>
              </div>
            </div>
          </div>
          <div class="dock-scene">
            <div class="station"></div>
            <div class="ship-dock" aria-label="守护者号"></div>
          </div>
          <p class="end-text">感谢每一位船员的付出，期待下一次起航！</p>
        </div>
      </section>
    </main>

    <footer id="footer">
      <div class="tip">使用方向键或点击“上一页 / 下一页”导航</div>
    </footer>

    <script src="./script.js"></script>
  </body>
  </html>



