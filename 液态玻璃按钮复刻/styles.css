/* iOS 26 液态玻璃按钮复刻样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 40px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

h1 {
    text-align: center;
    color: white;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 40px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

h2 {
    color: white;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.demo-section {
    margin-bottom: 50px;
    padding: 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 背景容器 */
.dark-background {
    background: #1a1a1a;
    padding: 40px;
    border-radius: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
}

.light-background {
    background: #f5f5f5;
    padding: 40px;
    border-radius: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
}

/* 液态玻璃按钮基础样式 */
.liquid-glass-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 14px 21px 14px 20px;
    border: none;
    border-radius: 100px;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 17px;
    font-weight: 590;
    line-height: 1.176;
    letter-spacing: -0.59%;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

/* 深色背景按钮样式 */
.btn-on-dark {
    background: linear-gradient(135deg, #E6E6E6 0%, rgba(51, 51, 51, 0.3) 100%);
    color: rgba(255, 255, 255, 0.5);
    
    /* 复杂的玻璃效果阴影 */
    box-shadow: 
        0px 1px 8px 0px rgba(0, 0, 0, 0.1),
        0px 0px 2px 0px rgba(0, 0, 0, 0.1),
        inset 0px 0px 16px 0px rgba(242, 242, 242, 1),
        inset 0px 0px 1px 1px rgba(153, 153, 153, 1),
        inset 0px 0px 1px 1px rgba(255, 255, 255, 0.15),
        inset -1px -1px 1px 0.5px rgba(255, 255, 255, 0.75),
        inset 1px 1px 1px 0.5px rgba(255, 255, 255, 0.75),
        inset 3px 3px 0.5px -3.5px rgba(255, 255, 255, 0.8),
        inset 3px 3px 0.5px -3.5px rgba(255, 255, 255, 0.75);
    
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
}

/* 浅色背景按钮样式 */
.btn-on-light {
    background: linear-gradient(135deg, #171717 0%, rgba(140, 140, 140, 0.25) 100%);
    color: #FFFFFF;
    
    /* 复杂的玻璃效果阴影 */
    box-shadow: 
        0px 1px 8px 0px rgba(0, 0, 0, 0.1),
        0px 0px 2px 0px rgba(0, 0, 0, 0.1),
        inset 0px 0px 8px 0px rgba(242, 242, 242, 1),
        inset 0px 0px 0px 1px rgba(166, 166, 166, 1),
        inset -2px -2px 0.5px -2px rgba(38, 38, 38, 1),
        inset 2px 2px 0.5px -2px rgba(38, 38, 38, 1),
        inset 3px 3px 0.5px -3.5px rgba(255, 255, 255, 1);
}

.btn-icon {
    font-size: 17px;
    display: inline-block;
}

.btn-text {
    font-weight: 590;
    white-space: nowrap;
}

/* 交互效果 */
.interactive:hover {
    transform: translateY(-2px) scale(1.02);
}

.interactive:active {
    transform: translateY(0) scale(0.98);
}

.btn-on-dark.interactive:hover {
    box-shadow: 
        0px 4px 16px 0px rgba(0, 0, 0, 0.2),
        0px 0px 4px 0px rgba(0, 0, 0, 0.15),
        inset 0px 0px 20px 0px rgba(242, 242, 242, 1),
        inset 0px 0px 2px 2px rgba(153, 153, 153, 1),
        inset 0px 0px 2px 2px rgba(255, 255, 255, 0.2),
        inset -2px -2px 2px 1px rgba(255, 255, 255, 0.8),
        inset 2px 2px 2px 1px rgba(255, 255, 255, 0.8);
}

.btn-on-light.interactive:hover {
    box-shadow: 
        0px 4px 16px 0px rgba(0, 0, 0, 0.2),
        0px 0px 4px 0px rgba(0, 0, 0, 0.15),
        inset 0px 0px 12px 0px rgba(242, 242, 242, 1),
        inset 0px 0px 0px 2px rgba(166, 166, 166, 1),
        inset -3px -3px 1px -2px rgba(38, 38, 38, 1),
        inset 3px 3px 1px -2px rgba(38, 38, 38, 1),
        inset 4px 4px 1px -3.5px rgba(255, 255, 255, 1);
}

/* 交互演示区域 */
.interactive-demo {
    display: flex;
    gap: 30px;
    justify-content: center;
    align-items: center;
    padding: 40px;
    background: linear-gradient(45deg, #1a1a1a 50%, #f5f5f5 50%);
    border-radius: 15px;
}

/* 对比区域 */
.comparison {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-top: 20px;
}

.original, .replica {
    text-align: center;
}

h3 {
    color: white;
    font-size: 1.2rem;
    margin-bottom: 20px;
}

.figma-image {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    margin: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.replica-buttons {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
    padding: 20px;
    background: linear-gradient(45deg, #1a1a1a 50%, #f5f5f5 50%);
    border-radius: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .comparison {
        grid-template-columns: 1fr;
    }
    
    .interactive-demo {
        flex-direction: column;
    }
    
    .container {
        padding: 20px;
    }
    
    h1 {
        font-size: 2rem;
    }
}
