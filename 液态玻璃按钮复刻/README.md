# iOS 26 液态玻璃按钮复刻

这是一个基于 Figma 设计的 iOS 26 风格液态玻璃按钮的 CSS 复刻项目。

## 项目概述

本项目使用 Figma MCP 工具从原始设计中提取了详细的设计规范，并使用纯 HTML/CSS 技术复刻了液态玻璃效果按钮。

## 原始设计信息

- **Figma 文件**: iOS 26 Liquid Glass Buttons (Community)
- **设计师**: Community 设计
- **节点 ID**: 2005-56
- **最后修改**: 2025-08-22

## 设计特征

### 按钮变体
1. **深色背景按钮** (Button / On Dark)
   - 背景: #E6E6E6 渐变到 rgba(51, 51, 51, 0.3)
   - 文字颜色: rgba(255, 255, 255, 0.5)
   - 复杂的内阴影和外阴影效果

2. **浅色背景按钮** (Button / On Light)
   - 背景: #171717 渐变到 rgba(140, 140, 140, 0.25)
   - 文字颜色: #FFFFFF
   - 深色调的玻璃效果

### 技术规格
- **圆角半径**: 100px (完全圆角)
- **字体**: SF Pro Display, 字重 590, 字号 17px
- **布局**: 水平排列，图标 + 文字
- **间距**: 内边距 14px 21px 14px 20px
- **背景模糊**: backdrop-filter: blur(12px)

## 实现亮点

### 1. 精确的玻璃效果
使用多层 box-shadow 实现复杂的玻璃质感：
- 外阴影：营造悬浮感
- 内阴影：创造凹陷和高光效果
- 背景模糊：增强玻璃透明感

### 2. 响应式交互
- 悬停效果：轻微上升和缩放
- 点击效果：下压反馈
- 平滑过渡动画

### 3. 设计对比
页面包含原始 Figma 设计图与 CSS 复刻版本的并排对比，便于验证复刻精度。

## 文件结构

```
液态玻璃按钮复刻/
├── index.html          # 主页面
├── styles.css          # 样式文件
└── README.md          # 项目说明

../figma-assets/
├── button-on-dark.png  # 原始深色按钮图像
└── button-on-light.png # 原始浅色按钮图像
```

## 使用方法

1. 直接在浏览器中打开 `index.html` 文件
2. 查看不同背景下的按钮效果
3. 体验交互动画效果
4. 对比原始设计与复刻版本

## 技术栈

- **HTML5**: 语义化结构
- **CSS3**: 
  - Flexbox 布局
  - CSS Grid 布局
  - 复杂 box-shadow 效果
  - backdrop-filter 背景模糊
  - CSS 变换和过渡动画
  - 响应式设计

## 浏览器兼容性

- Chrome 76+ (完整支持)
- Safari 14+ (完整支持)
- Firefox 103+ (部分支持，backdrop-filter 需要启用)
- Edge 79+ (完整支持)

## 开发工具

本项目使用了以下工具：
- **Figma MCP**: 用于提取设计规范和下载资源
- **Augment Agent**: AI 辅助开发和代码生成

## 许可证

本项目仅用于学习和演示目的。原始设计版权归 Figma Community 设计师所有。
