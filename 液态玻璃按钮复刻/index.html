<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iOS 26 液态玻璃按钮复刻</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h1>iOS 26 液态玻璃按钮复刻</h1>
        
        <div class="demo-section">
            <h2>深色背景按钮</h2>
            <div class="dark-background">
                <button class="liquid-glass-btn btn-on-dark">
                    <span class="btn-icon">􀤑</span>
                    <span class="btn-text">主持</span>
                </button>
            </div>
        </div>

        <div class="demo-section">
            <h2>浅色背景按钮</h2>
            <div class="light-background">
                <button class="liquid-glass-btn btn-on-light">
                    <span class="btn-icon">􀤑</span>
                    <span class="btn-text">首页</span>
                </button>
            </div>
        </div>

        <div class="demo-section">
            <h2>交互演示</h2>
            <div class="interactive-demo">
                <button class="liquid-glass-btn btn-on-dark interactive">
                    <span class="btn-icon">􀤑</span>
                    <span class="btn-text">点击我</span>
                </button>
                
                <button class="liquid-glass-btn btn-on-light interactive">
                    <span class="btn-icon">􀤑</span>
                    <span class="btn-text">悬停效果</span>
                </button>
            </div>
        </div>

        <div class="demo-section">
            <h2>原始设计对比</h2>
            <div class="comparison">
                <div class="original">
                    <h3>原始 Figma 设计</h3>
                    <img src="../figma-assets/button-on-dark.png" alt="原始深色按钮" class="figma-image">
                    <img src="../figma-assets/button-on-light.png" alt="原始浅色按钮" class="figma-image">
                </div>
                <div class="replica">
                    <h3>CSS 复刻版本</h3>
                    <div class="replica-buttons">
                        <button class="liquid-glass-btn btn-on-dark">
                            <span class="btn-icon">􀤑</span>
                            <span class="btn-text">主持</span>
                        </button>
                        <button class="liquid-glass-btn btn-on-light">
                            <span class="btn-icon">􀤑</span>
                            <span class="btn-text">首页</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
